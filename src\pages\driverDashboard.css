/* src/pages/driverDashboard.css */

.driver-dashboard {
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.dashboard-content {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.map-section {
    flex: 2;
    height: 600px; /* Adjust height as needed */
    border: 1px solid #ccc;
    border-radius: 8px;
    overflow: hidden;
    position: relative; /* Added for positioning context */
}

.control-panel-section {
    flex: 1;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.control-panel-section h2 {
    margin-top: 0;
    color: #333;
}

.control-panel-section button {
    margin-right: 10px;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    background-color: #007bff;
    color: white;
}

.control-panel-section button:hover {
    background-color: #0056b3;
}

.map-overlay-text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8); /* Semi-transparent white background */
    color: #333;
    font-size: 1.2em;
    font-weight: bold;
    text-align: center;
    z-index: 10; /* Ensure it's above the map */
}

.driver-image{
    align-items: center;
}


