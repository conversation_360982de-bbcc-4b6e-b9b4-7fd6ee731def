{"name": "navis", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@flaticon/flaticon-uicons": "^3.3.1", "@mui/base": "^5.0.0-beta.40", "@mui/joy": "^5.0.0-beta.47", "@mui/material": "^5.16.1", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@mui/utils": "^5.16.1", "@mui/x-charts": "^7.9.0", "@react-google-maps/api": "^2.19.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@vis.gl/react-google-maps": "^1.1.0", "ajv": "^6.12.6", "ajv-keywords": "^3.5.2", "antd": "^5.20.0", "assert": "^2.1.0", "aws-sdk": "^2.1658.0", "axios": "^1.7.2", "browserify-zlib": "^0.2.0", "cors": "^2.8.5", "crypto-browserify": "^3.12.0", "date-fns": "^3.6.0", "dotenv": "^16.6.1", "express": "^4.21.2", "firebase": "^10.12.5", "firebase-admin": "^12.3.0", "formik": "^2.4.6", "formik-antd": "^3.0.0-beta.11", "leaflet": "^1.9.4", "mongodb": "^6.19.0", "os-browserify": "^0.3.0", "process": "^0.11.10", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-places-autocomplete": "^4.0.1", "react-leaflet": "^4.2.1", "react-modal": "^3.16.1", "react-places-autocomplete": "^7.3.0", "react-router-dom": "^6.24.1", "react-scripts": "5.0.1", "react-table": "^7.8.0", "react-ui-animate": "^3.3.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "styled-components": "^6.1.11", "tailwindcss": "^3.4.6", "timers-browserify": "^2.0.12", "uuid": "^10.0.0", "web-vitals": "^2.1.4", "zlib-browserify": "^0.0.3"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "test:api": "node test-api.js", "start:server": "node local-server.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-app-rewired": "^2.2.1"}}