// import React from 'react';
// import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
// import Dashboard from './pages/Dashboard';
// import Vehicles from './pages/Tracks';
// import Drivers from './pages/Drivers';
// import Shipments from './pages/Shipments';
// import Customers from './pages/Customers';
// import Login from './pages/Login';
// import ProtectedRoute from './components/ProtectedRoutes';

// function App() {
//   return (
//     <Router>
//       <Routes>
//         <Route path="/login" element={<Login />} />
//         <Route path="/" element={<ProtectedRoute component={Dashboard} />} />
//         <Route path="/vehicles" element={<ProtectedRoute component={Vehicles} />} />
//         <Route path="/drivers" element={<ProtectedRoute component={Drivers} />} />
//         <Route path="/shipments" element={<ProtectedRoute component={Shipments} />} />
//         <Route path="/customers" element={<ProtectedRoute component={Customers} />} />
//         <Route path="/customers" element={<ProtectedRoute component={Customers} />} />

//       </Routes>

//     </Router>
//   );
// }

// export default App;
