react-dom.development.js:798 Error: <path> attribute d: Expected number, "M undefined 275 L …". setValueForProperty @ react-dom.development.js:798 setInitialDOMProperties @ react-dom.development.js:9720 setInitialProperties @ react-dom.development.js:9921 finalizeInitialChildren @ react-dom.development.js:10950 completeWork @ react-dom.development.js:22232 completeUnitOfWork @ react-dom.development.js:26635 performUnitOfWork @ react-dom.development.js:26607 workLoopSync @ react-dom.development.js:26505 renderRootSync @ react-dom.development.js:26473 performConcurrentWorkOnRoot @ react-dom.development.js:25777 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 AuthContext.js:74 AuthContext: Logging out... AuthContext.js:80 AuthContext: onAuthStateChanged triggered. User: null AuthContext.js:87 AuthContext: No user found. Setting user state to null. AuthContext.js:91 AuthContext: Loading set to false. AuthContext.js:80 AuthContext: onAuthStateChanged triggered. User: UserImpl {providerId: 'firebase', proactiveRefresh: ProactiveRefresh, reloadUserInfo: {…}, reloadListener: null, uid: '5xXwPbP3J3XY58ITEPEmMGpanNC3', …} AuthContext.js:83 AuthContext: User document data from Firestore: {company: 'Navis Logistics', accountType: 'driver', imageUrl: 'https://firebasestorage.googleapis.com/v0/b/navis-…=media&token=23878d82-efe2-4c0b-bb06-5b5edcdfb7b0', email: '<EMAIL>', username: 'Test Driver'} AuthContext.js:85 AuthContext: User state set to: {providerId: 'firebase', proactiveRefresh: ProactiveRefresh, reloadUserInfo: {…}, reloadListener: null, uid: '5xXwPbP3J3XY58ITEPEmMGpanNC3', …} AuthContext.js:91 AuthContext: Loading set to false. react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533 react-dom.development.js:86 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render. Error Component Stack at Navigate (components.tsx:280:1) at DriverProtectedRoute (index.js:54:1) at RenderedRoute (hooks.tsx:657:1) at Outlet (components.tsx:333:1) at div (<anonymous>) at Root (root.jsx:9:1) at RenderedRoute (hooks.tsx:657:1) at RenderErrorBoundary (hooks.tsx:583:1) at DataRoutes (index.tsx:752:1) at Router (components.tsx:413:1) at RouterProvider (index.tsx:487:1) at DriverAuthProvider (DriverAuthContext.js:7:1) at DatabaseProvider (DatabaseContext.js:10:1) at AuthProvider (AuthContext.js:13:1) at FirebaseProvider (firebaseContext.js:46:1) at LoadScript (LoadScript.tsx:36:1) overrideMethod @ hook.js:608 printWarning @ react-dom.development.js:86 error @ react-dom.development.js:60 checkForNestedUpdates @ react-dom.development.js:27339 scheduleUpdateOnFiber @ react-dom.development.js:25514 dispatchSetState @ react-dom.development.js:16708 (anonymous) @ index.tsx:544 (anonymous) @ index.tsx:511 (anonymous) @ index.tsx:544 (anonymous) @ router.ts:1161 updateState @ router.ts:1161 completeNavigation @ router.ts:1290 handleLoaders @ router.ts:1920 startNavigation @ router.ts:1627 navigate @ router.ts:1421 (anonymous) @ hooks.tsx:1088 (anonymous) @ components.tsx:315 commitHookEffectListMount @ react-dom.development.js:23189 commitPassiveMountOnFiber @ react-dom.development.js:24965 commitPassiveMountEffects_complete @ react-dom.development.js:24930 commitPassiveMountEffects_begin @ react-dom.development.js:24917 commitPassiveMountEffects @ react-dom.development.js:24905 flushPassiveEffectsImpl @ react-dom.development.js:27078 flushPassiveEffects @ react-dom.development.js:27023 (anonymous) @ react-dom.development.js:26808 workLoop @ scheduler.development.js:266 flushWork @ scheduler.development.js:239 performWorkUntilDeadline @ scheduler.development.js:533as