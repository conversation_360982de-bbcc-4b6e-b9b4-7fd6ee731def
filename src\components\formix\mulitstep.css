.details__wrapper{
    /* background-color: red; */
width: 100%;
}


.tell-us-text{
    margin-top: .5rem;
    font-size: 2rem;
    line-height: 3.25rem;
    font-weight: 500;
    letter-spacing: 0;
    text-transform: none;
    font-variant-numeric: unset;
    margin-bottom: .5rem;
}




.form__item{
    display: flex;
    flex-direction: column;
}

.tll-input{
    margin-bottom: 12px;
    /* height: 3rem; */
    width: 100%;
}

label{
    font-size: 1rem;
    line-height: 1.5rem;
    font-weight: 400;
    letter-spacing: 0;
    text-align: left;
    text-transform: none;
    margin-bottom: 1%;
    font-variant-numeric: unset;
}

.ml-btn{
    width: 26% !important;

}

.number{
    width: 100%;
}



.label{
    font-size: 1rem;
    line-height: 1.5rem;
    font-weight: 400;
    margin-top: .9em;
    letter-spacing: 0;
    text-align: left;
    text-transform: none;
    margin-bottom: 1%;
    font-variant-numeric: unset;
}


.back-btn-ml{
margin-bottom: .5em;
}


.rv-btn{
    margin-top: .5em;
}


.error__feedback{
    color: red;
    font-size: small;
}


#lastone{
    display: flex;
    margin-top: 2%;
}

.right_section{
/* background-color: #eeeff04d; */
    width: 100%;
    margin-left: 0.3em;
    display: flex;
        border-radius: 12px;
    margin-right: 0.3rem;
    padding-left: 1rem;
    padding-right: 1rem;
    align-items: center;
    background-color: white;
    justify-content: center;
}

.data-last{
    width: 100%;
    background-color: white;
    padding-left: 1rem;
    padding-bottom: 1rem;
    border-radius: 12px;
    padding-right: 1rem;
}

.front-page{
    /* border-radius: 50px; */
    width: 15rem;
    margin-top: 4%;
}

.truck-info-front {
    font-size: 1rem;
    line-height: 2.25rem;
    font-weight: 600;
    margin-bottom: .5rem;
}


.rest{
    font-size: 1.6rem;
    line-height: 2.25rem;
    font-weight: 600;
    margin-top: 1%;
    letter-spacing: 0;
    text-transform: none;
    font-variant-numeric: unset;
    margin-bottom: .5rem;
}

.congs{
    font-size: 1rem;
    line-height: 2.25rem;
    font-weight: 400;
    margin-top: 1%;
    width: 40rem;
    letter-spacing: 0;
    text-transform: none;
    font-variant-numeric: unset;
    margin-bottom: .5rem;
}

.est-time{
    margin-top: 5%;
}


.text-dets-span{
    color: black;
    margin-left: .2rem;

}

.text-dets{
    color: gray !important;
    margin-bottom: 0px;
    font-size: small;
    font-weight: 500;
}

.mapOba{
    width: 100%;
    border-radius: 12px;
    padding: .6rem;
    background-color: white;
}

#auto_i{
    width: 100%;
}

.ml-btn2{
    margin-left: 1%;
    background-color: #216DF0;
    color: white;
}